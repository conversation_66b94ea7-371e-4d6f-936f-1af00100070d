# 问题1
1.缺失：system/index.html、tests/index.html
2.顶部的“项目列表”下拉框，没有项目展示，刷新后也没有。
3.上传文档后，前端进度弹出框一直转圈，不消失。后端服务调用失败（如400、500错误）时，前端的弹出框也没有销售，并且也不显示具体的错误信息。
4."从文档提取需求"的对话框，在选择文档后，“按章节批次处理”功能，可以再选择章节分隔符，在调用get_document_sections获取章节时用于区分章节。
5.调用大模型提取需求功能点功能，增加多线程并发处理能力，线程数在配置文件中配置。每个线程平均处理文档的多个章节。
6.大模型生成测试用例功能、生成自动化测试用例的功能，也增加多线程处理能力，每个线程平均处理若干个功能点的测试用例。
7.“需求功能点管理”界面，点击“编辑”没有反应；列表界面没有批量删除功能。
8.knowledge_manager.py_split_text的切块功能，可以按照markdown的###分隔符(可在配置文件中配置)进行切块。
9.知识库管理界面的"知识库搜索"，在有结果后,“进度框”没有消失。

# 问题2
解决以下问题，实现中间不用询问，继续完成全部任务：

1.“生成测试用例”对话框“选择需求功能点”的名称都是：undefined
2."测试用例生成失败"，进度框没有关闭。
3.测试用例生成后没有从持久保存，界面的/tests/api/list就没有查询到测试用例。
4."文档上传"成功后，进度框没有关闭，“知识库搜索”出结果后，进度框未关闭；检查下项目的其他功能，进度框可能都有同样问题。
5.大模型根据需求文档抽取的功能点，只有描述，缺失标题, 没有持久化保存。上次修复之前的功能是有保存的。
6.各个列表界面，增加每页行数的选择：10、30、50.
7.知识库移除文档失败: Expected include item to be one of embeddings, documents, metadatas, got ids
8.根据"项目提示词.md"要求的功能，检查项目全部代码是否有遗漏、功能是否完善，不要再有错误。


# 问题3
解决以下问题，实现中间不用询问，继续完成全部任务：

1. 测试用例列表，点击查看、编辑、删除都没有反应
2. 测试用例列表，增加友好：点击名称跳转到详情页面
3. 根据功能点生成的测试用例，管理需求都是：无关联需求

# 问题4
解决以下问题和功能，中间不用询问，继续完成全部任务：

1. "正在删除需求..."对话框，在后台执行完毕后不关闭。
2. "需求功能点管理"界面，点击需求编号和标题，可跳转到编辑页面，鼠标移动到标题，可浮动显示详细的需求描述。
3. "需求功能点管理"列表界面，按照编号排序
4. "测试管理"列表界面，点击“关联需求”，可弹出需求详情页面。
5. "需求功能点管理"列表界面，不需要独立的“全选”框，使用列表标题的复选框做为全选功能，参考：功能点管理 的列表。
6. "测试管理"菜单下加子菜单，把“自动化测试用例”的管理独立出来。
7. "知识库"按照MARKDOWN_SEPARATOR分隔符切块时，还要检查分隔符后是否跟着空格+章节编号，如：### 6.4.2. 监控组件功能扩展 ，这种才是一个章节。使用大模型提取功能点时，也是按照这个规则提取章节进行批次处理。

# 问题5：
解决以下问题和功能，中间不用询问，继续完成全部任务并测试验证：

1. "测试管理"菜单在 点击子菜单后，又缩回菜单了。请参考“需求管理”菜单的实现方式修改。
2. "用户测试用例"列表界面，不需要独立的“全选”框，使用列表标题的复选框做为全选功能，参考：功能点管理 的列表。
3. “自动化测试用例”和“用户测试用例”是两类不同的测试用例，应该使用不同的存储。
4. “自动化测试用例”根据“功能测试”类型的用例生成，输出midscenejs可以执行的格式（参考：https://midscenejs.com/）。
5. “自动化测试用例”支持在线增删改查、分页列表展示、搜索，可以手动添加，支持批量删除.
6. “自动化测试用例”与“用户测试用例”建立关联关系，可以在界面查看“自动化用例”对应的“用户测试用例”
7. 在"生成测试用例"对话框，选择"生成自动化测试用例"时，会先生成“用户测试用例"，再根据"功能测试类型"的用例生成自动化用例。
8. 也可以在“用户测试用例”列表，选择多个用例，批量生成自动化用例，支持多线程。
9. 大模型自动生成的需求功能点、用户测试用例、自动化测试用例，都增加一个修改时间，标记这个数据是否在大模型生成后修改过，并在首页增加统计：可在总数后加括号显示，如：总数（大模型生成数量/修改数量）。
