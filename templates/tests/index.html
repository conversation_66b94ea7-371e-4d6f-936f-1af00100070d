{% extends "base.html" %}

{% block title %}测试管理 - AI智能测试平台{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="/">首页</a></li>
<li class="breadcrumb-item active">测试管理</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-vial me-2"></i>测试管理
            </h1>
            <div class="btn-group">
                <button class="btn btn-primary" onclick="showGenerateModal()">
                    <i class="fas fa-magic me-1"></i>生成测试用例
                </button>
                <button class="btn btn-success" onclick="showCreateModal()">
                    <i class="fas fa-plus me-1"></i>创建测试用例
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 搜索和过滤 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="search-box">
            <input type="text" class="form-control" id="searchInput" placeholder="搜索测试用例...">
            <i class="fas fa-search search-icon"></i>
        </div>
    </div>
    <div class="col-md-2">
        <select class="form-select" id="statusFilter">
            <option value="">所有状态</option>
            <option value="pending">待执行</option>
            <option value="running">执行中</option>
            <option value="passed">通过</option>
            <option value="failed">失败</option>
            <option value="skipped">跳过</option>
        </select>
    </div>
    <div class="col-md-2">
        <select class="form-select" id="typeFilter">
            <option value="">所有类型</option>
            <option value="unit">单元测试</option>
            <option value="integration">集成测试</option>
            <option value="functional">功能测试</option>
            <option value="performance">性能测试</option>
            <option value="security">安全测试</option>
        </select>
    </div>
    <div class="col-md-2">
        <select class="form-select" id="pageSizeSelect" onchange="changePageSize()">
            <option value="10" selected>10条</option>
            <option value="30">30条</option>
            <option value="50">50条</option>
        </select>
    </div>
    <div class="col-md-3 text-end">
        <button class="btn btn-outline-secondary" onclick="refreshTests()">
            <i class="fas fa-sync me-1"></i>刷新
        </button>
    </div>
</div>

<!-- 批量操作 -->
<div class="row mb-3">
    <div class="col-12">
        <div class="d-flex align-items-center">
            <div class="form-check me-3">
                <input class="form-check-input" type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                <label class="form-check-label" for="selectAll">全选</label>
            </div>
            <div class="btn-group" id="batchActions" style="display: none;">
                <button class="btn btn-sm btn-outline-primary" onclick="batchExecute()">
                    <i class="fas fa-play me-1"></i>批量执行
                </button>
                <button class="btn btn-sm btn-outline-danger" onclick="batchDelete()">
                    <i class="fas fa-trash me-1"></i>批量删除
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 测试用例列表 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th width="40">
                                    <input type="checkbox" id="headerCheckbox" onchange="toggleSelectAll()">
                                </th>
                                <th>测试用例名称</th>
                                <th>类型</th>
                                <th>状态</th>
                                <th>关联需求</th>
                                <th>最后执行</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="testsTableBody">
                            <!-- 测试用例列表将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div id="pagination"></div>
            </div>
        </div>
    </div>
</div>

<!-- 生成测试用例模态框 -->
<div class="modal fade" id="generateModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">生成测试用例</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="generateForm">
                    <div class="mb-3">
                        <label for="requirementSelect" class="form-label">选择需求功能点</label>
                        <select class="form-select" id="requirementSelect" multiple size="8">
                            <!-- 需求列表将动态加载 -->
                        </select>
                        <div class="form-text">按住Ctrl键可多选</div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="testType" class="form-label">测试类型</label>
                                <select class="form-select" id="testType">
                                    <option value="unit">单元测试</option>
                                    <option value="integration">集成测试</option>
                                    <option value="functional" selected>功能测试</option>
                                    <option value="performance">性能测试</option>
                                    <option value="security">安全测试</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="generateThreads" class="form-label">并发线程数</label>
                                <input type="number" class="form-control" id="generateThreads" min="1" max="10" value="4">
                                <div class="form-text">用于并发生成测试用例</div>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="includeAutomation" checked>
                            <label class="form-check-label" for="includeAutomation">
                                生成自动化测试用例
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="generateTestCases()">开始生成</button>
            </div>
        </div>
    </div>
</div>

<!-- 需求详情模态框 -->
<div class="modal fade" id="requirementDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">需求详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="requirementDetailContent">
                    <!-- 需求详情内容将通过JavaScript动态加载 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentPage = 1;
let currentSearch = '';
let currentStatusFilter = '';
let currentTypeFilter = '';
let selectedTests = new Set();

$(document).ready(function() {
    loadTests();
    loadRequirements();

    // 搜索输入框事件
    $('#searchInput').on('input', debounce(function() {
        currentSearch = $(this).val();
        currentPage = 1;
        loadTests();
    }, 500));

    // 过滤器事件
    $('#statusFilter, #typeFilter').on('change', function() {
        currentStatusFilter = $('#statusFilter').val();
        currentTypeFilter = $('#typeFilter').val();
        currentPage = 1;
        loadTests();
    });
});

// 加载测试用例列表
function loadTests() {
    const pageSize = parseInt($('#pageSizeSelect').val()) || 10;
    const params = {
        page: currentPage,
        page_size: pageSize
    };

    if (currentSearch) params.search = currentSearch;
    if (currentStatusFilter) params.status = currentStatusFilter;
    if (currentTypeFilter) params.type = currentTypeFilter;

    $.get('/tests/api/list', params, function(response) {
        if (response.success) {
            renderTestsTable(response.data.records);
            initPagination('pagination', response.data.total_pages, currentPage, function(page) {
                currentPage = page;
                loadTests();
            });
        } else {
            showNotification('加载测试用例失败: ' + response.message, 'error');
        }
    }).fail(function() {
        showNotification('加载测试用例失败', 'error');
    });
}

// 渲染测试用例表格
function renderTestsTable(tests) {
    const tbody = $('#testsTableBody');
    tbody.empty();

    if (tests.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="7" class="text-center text-muted py-4">
                    <i class="fas fa-vial fa-2x mb-2"></i><br>
                    暂无测试用例数据
                </td>
            </tr>
        `);
        return;
    }

    tests.forEach(function(test) {
        const statusBadge = getStatusBadge(test.status);
        const typeBadge = getTypeBadge(test.type);

        const row = $(`
            <tr>
                <td>
                    <input type="checkbox" class="test-checkbox" value="${test.id}" onchange="updateSelection()">
                </td>
                <td>
                    <a href="javascript:void(0)" onclick="viewTest('${test.id}')" class="text-decoration-none">
                        <strong class="text-primary">${test.name}</strong>
                    </a>
                    <br><small class="text-muted">${test.description || '暂无描述'}</small>
                </td>
                <td>${typeBadge}</td>
                <td>${statusBadge}</td>
                <td>
                    ${test.requirement_ids && test.requirement_ids.length > 0 ?
                        `<a href="javascript:void(0)" onclick="showRequirementDetails('${test.requirement_ids[0]}')" class="text-decoration-none">
                            <small>${test.requirement_name || '关联需求'}</small>
                        </a>` :
                        '<small class="text-muted">无关联需求</small>'
                    }
                </td>
                <td>
                    <small class="text-muted">${test.last_executed ? formatDate(test.last_executed) : '未执行'}</small>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="viewTest('${test.id}')" title="查看详情">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-success" onclick="executeTest('${test.id}')" title="执行测试">
                            <i class="fas fa-play"></i>
                        </button>
                        <button class="btn btn-outline-warning" onclick="editTest('${test.id}')" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteTest('${test.id}', '${test.name}')" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `);
        tbody.append(row);
    });
}

// 获取状态徽章
function getStatusBadge(status) {
    const badges = {
        'pending': '<span class="badge bg-secondary">待执行</span>',
        'running': '<span class="badge bg-primary">执行中</span>',
        'passed': '<span class="badge bg-success">通过</span>',
        'failed': '<span class="badge bg-danger">失败</span>',
        'skipped': '<span class="badge bg-warning">跳过</span>'
    };
    return badges[status] || '<span class="badge bg-secondary">未知</span>';
}

// 获取类型徽章
function getTypeBadge(type) {
    const badges = {
        'unit': '<span class="badge bg-info">单元测试</span>',
        'integration': '<span class="badge bg-primary">集成测试</span>',
        'functional': '<span class="badge bg-success">功能测试</span>',
        'performance': '<span class="badge bg-warning">性能测试</span>',
        'security': '<span class="badge bg-danger">安全测试</span>'
    };
    return badges[type] || '<span class="badge bg-secondary">其他</span>';
}

// 加载需求列表
function loadRequirements() {
    $.get('/requirements/api/list', {page_size: 1000}, function(response) {
        if (response.success) {
            const select = $('#requirementSelect');
            select.empty();

            if (response.data.records && response.data.records.length > 0) {
                response.data.records.forEach(function(req) {
                    // 优先使用title，如果没有title则使用description，都没有则使用编号
                    const displayText = req.title || req.description || req.number || `需求-${req.id}`;
                    const optionText = req.number ? `${req.number}: ${displayText}` : displayText;
                    select.append(`<option value="${req.id}">${optionText}</option>`);
                });
            } else {
                select.append('<option disabled>暂无需求功能点</option>');
            }
        } else {
            showNotification('加载需求列表失败: ' + (response.message || '未知错误'), 'error');
        }
    }).fail(function(xhr, status, error) {
        console.error('加载需求列表失败:', error);
        showNotification('加载需求列表失败: 网络错误', 'error');
    });
}

// 显示生成模态框
function showGenerateModal() {
    const modal = new bootstrap.Modal($('#generateModal')[0]);
    modal.show();
}

// 生成测试用例
function generateTestCases() {
    const selectedRequirements = $('#requirementSelect').val();
    if (!selectedRequirements || selectedRequirements.length === 0) {
        showNotification('请选择至少一个需求功能点', 'warning');
        return;
    }

    const data = {
        requirement_ids: selectedRequirements,
        test_type: $('#testType').val(),
        include_automation: $('#includeAutomation').is(':checked'),
        max_threads: parseInt($('#generateThreads').val()) || 4
    };

    showProgress('正在生成测试用例，请稍候...');
    bootstrap.Modal.getInstance($('#generateModal')[0]).hide();

    $.ajax({
        url: '/tests/api/generate',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(data),
        timeout: 300000, // 5分钟超时
        success: function(response) {
            hideProgress();
            if (response && response.success) {
                const message = response.data?.message || response.message || '测试用例生成成功';
                showNotification(message, 'success');
                loadTests();
            } else {
                const errorMessage = response?.message || '生成测试用例失败';
                showNotification('生成测试用例失败: ' + errorMessage, 'error');
            }
        },
        error: function(xhr, status, error) {
            hideProgress();
            console.error('生成测试用例错误:', xhr, status, error);

            let errorMessage = '生成测试用例失败';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            } else if (status === 'timeout') {
                errorMessage = '生成超时，请稍后重试';
            } else if (xhr.status >= 500) {
                errorMessage = '服务器错误，请稍后重试';
            } else if (xhr.status >= 400) {
                errorMessage = '请求错误: ' + (xhr.responseJSON?.message || xhr.statusText);
            }

            showNotification(errorMessage, 'error');
        },
        complete: function() {
            // 确保进度框被隐藏
            setTimeout(hideProgress, 100);
        }
    });
}

// 更新选择状态
function updateSelection() {
    selectedTests.clear();
    $('.test-checkbox:checked').each(function() {
        selectedTests.add($(this).val());
    });

    const batchActions = $('#batchActions');
    if (selectedTests.size > 0) {
        batchActions.show();
    } else {
        batchActions.hide();
    }

    // 更新全选状态
    const totalCheckboxes = $('.test-checkbox').length;
    const checkedCheckboxes = $('.test-checkbox:checked').length;
    const selectAllCheckbox = $('#selectAll');

    if (checkedCheckboxes === 0) {
        selectAllCheckbox.prop('indeterminate', false);
        selectAllCheckbox.prop('checked', false);
    } else if (checkedCheckboxes === totalCheckboxes) {
        selectAllCheckbox.prop('indeterminate', false);
        selectAllCheckbox.prop('checked', true);
    } else {
        selectAllCheckbox.prop('indeterminate', true);
    }
}

// 切换全选
function toggleSelectAll() {
    const isChecked = $('#selectAll').is(':checked');
    $('.test-checkbox').prop('checked', isChecked);
    updateSelection();
}

// 批量执行
function batchExecute() {
    if (selectedTests.size === 0) {
        showNotification('请选择要执行的测试用例', 'warning');
        return;
    }

    if (confirm(`确定要执行选中的 ${selectedTests.size} 个测试用例吗？`)) {
        showProgress('正在批量执行测试用例...');

        $.ajax({
            url: '/tests/api/batch_execute',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({test_ids: Array.from(selectedTests)}),
            success: function(response) {
                hideProgress();
                if (response.success) {
                    showNotification('批量执行已开始', 'success');
                    loadTests();
                } else {
                    showNotification('批量执行失败: ' + response.message, 'error');
                }
            },
            error: function() {
                hideProgress();
                showNotification('批量执行失败', 'error');
            }
        });
    }
}

// 批量删除
function batchDelete() {
    if (selectedTests.size === 0) {
        showNotification('请选择要删除的测试用例', 'warning');
        return;
    }

    if (confirm(`确定要删除选中的 ${selectedTests.size} 个测试用例吗？此操作不可恢复。`)) {
        $.ajax({
            url: '/tests/api/batch_delete',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({test_case_ids: Array.from(selectedTests)}),
            success: function(response) {
                if (response.success) {
                    showNotification('批量删除成功', 'success');
                    loadTests();
                    selectedTests.clear();
                    $('#batchActions').hide();
                } else {
                    showNotification('批量删除失败: ' + response.message, 'error');
                }
            },
            error: function() {
                showNotification('批量删除失败', 'error');
            }
        });
    }
}

// 刷新测试列表
function refreshTests() {
    currentPage = 1;
    currentSearch = '';
    currentStatusFilter = '';
    currentTypeFilter = '';
    $('#searchInput').val('');
    $('#statusFilter').val('');
    $('#typeFilter').val('');
    loadTests();
}

// 改变分页大小
function changePageSize() {
    currentPage = 1; // 重置到第一页
    loadTests();
}

// 查看测试用例详情
function viewTest(testId) {
    $.get(`/tests/api/${testId}`, function(response) {
        if (response.success) {
            showTestDetails(response.data);
        } else {
            showNotification('获取测试用例详情失败: ' + response.message, 'error');
        }
    }).fail(function() {
        showNotification('获取测试用例详情失败', 'error');
    });
}

// 显示测试用例详情
function showTestDetails(test) {
    const modal = $(`
        <div class="modal fade" id="testDetailModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">测试用例详情</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>名称:</strong> ${test.name || '-'}</p>
                                <p><strong>类型:</strong> ${test.type || '-'}</p>
                                <p><strong>优先级:</strong> ${test.priority || '-'}</p>
                                <p><strong>状态:</strong> ${test.status || '-'}</p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>创建时间:</strong> ${test.created_at ? formatDate(test.created_at) : '-'}</p>
                                <p><strong>更新时间:</strong> ${test.updated_at ? formatDate(test.updated_at) : '-'}</p>
                                <p><strong>最后执行:</strong> ${test.last_executed ? formatDate(test.last_executed) : '未执行'}</p>
                            </div>
                        </div>
                        <div class="mt-3">
                            <p><strong>描述:</strong></p>
                            <div class="border p-3 bg-light">${test.description || '无描述'}</div>
                        </div>
                        <div class="mt-3">
                            <p><strong>测试步骤:</strong></p>
                            <div class="border p-3 bg-light">${test.steps ? test.steps.map(step => `<div class="step-item">${step}</div>`).join('') : '无测试步骤'}</div>
                        </div>
                        <div class="mt-3">
                            <p><strong>预期结果:</strong></p>
                            <div class="border p-3 bg-light">${test.expected_result || '无预期结果'}</div>
                        </div>
                        ${test.requirement_ids && test.requirement_ids.length > 0 ?
                            `<div class="mt-3">
                                <p><strong>关联需求:</strong></p>
                                <div class="border p-3 bg-light">${test.requirement_ids.join(', ')}</div>
                            </div>` : ''
                        }
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        <button type="button" class="btn btn-primary" onclick="editTest('${test.id}')">编辑</button>
                    </div>
                </div>
            </div>
        </div>
    `);

    $('body').append(modal);
    const modalInstance = new bootstrap.Modal(modal[0]);
    modalInstance.show();

    // 模态框关闭后移除DOM元素
    modal.on('hidden.bs.modal', function() {
        modal.remove();
    });
}

// 编辑测试用例
function editTest(testId) {
    // 关闭详情模态框
    const detailModal = bootstrap.Modal.getInstance($('#testDetailModal')[0]);
    if (detailModal) {
        detailModal.hide();
    }

    $.get(`/tests/api/${testId}`, function(response) {
        if (response.success) {
            showEditTestModal(response.data);
        } else {
            showNotification('获取测试用例信息失败: ' + response.message, 'error');
        }
    }).fail(function() {
        showNotification('获取测试用例信息失败', 'error');
    });
}

// 显示编辑测试用例模态框
function showEditTestModal(test) {
    const modal = $(`
        <div class="modal fade" id="editTestModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">编辑测试用例</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="editTestForm">
                            <input type="hidden" id="editTestId" value="${test.id}">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="editTestName" class="form-label">测试用例名称</label>
                                        <input type="text" class="form-control" id="editTestName" value="${test.name || ''}" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="editTestType" class="form-label">类型</label>
                                        <select class="form-select" id="editTestType">
                                            <option value="functional" ${test.type === 'functional' ? 'selected' : ''}>功能测试</option>
                                            <option value="performance" ${test.type === 'performance' ? 'selected' : ''}>性能测试</option>
                                            <option value="security" ${test.type === 'security' ? 'selected' : ''}>安全测试</option>
                                            <option value="integration" ${test.type === 'integration' ? 'selected' : ''}>集成测试</option>
                                            <option value="unit" ${test.type === 'unit' ? 'selected' : ''}>单元测试</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="editTestPriority" class="form-label">优先级</label>
                                        <select class="form-select" id="editTestPriority">
                                            <option value="high" ${test.priority === 'high' ? 'selected' : ''}>高</option>
                                            <option value="medium" ${test.priority === 'medium' ? 'selected' : ''}>中</option>
                                            <option value="low" ${test.priority === 'low' ? 'selected' : ''}>低</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="editTestStatus" class="form-label">状态</label>
                                        <select class="form-select" id="editTestStatus">
                                            <option value="pending" ${test.status === 'pending' ? 'selected' : ''}>待执行</option>
                                            <option value="running" ${test.status === 'running' ? 'selected' : ''}>执行中</option>
                                            <option value="passed" ${test.status === 'passed' ? 'selected' : ''}>通过</option>
                                            <option value="failed" ${test.status === 'failed' ? 'selected' : ''}>失败</option>
                                            <option value="skipped" ${test.status === 'skipped' ? 'selected' : ''}>跳过</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="editTestDescription" class="form-label">描述</label>
                                <textarea class="form-control" id="editTestDescription" rows="3">${test.description || ''}</textarea>
                            </div>
                            <div class="mb-3">
                                <label for="editTestSteps" class="form-label">测试步骤</label>
                                <textarea class="form-control" id="editTestSteps" rows="4">${test.steps ? test.steps.map(step => `${step}`).join('\n') : ''}</textarea>
                            </div>
                            <div class="mb-3">
                                <label for="editTestExpectedResult" class="form-label">预期结果</label>
                                <textarea class="form-control" id="editTestExpectedResult" rows="3">${test.expected_result || ''}</textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" onclick="saveTestChanges()">保存</button>
                    </div>
                </div>
            </div>
        </div>
    `);

    $('body').append(modal);
    const modalInstance = new bootstrap.Modal(modal[0]);
    modalInstance.show();

    // 模态框关闭后移除DOM元素
    modal.on('hidden.bs.modal', function() {
        modal.remove();
    });
}

// 保存测试用例修改
function saveTestChanges() {
    const testId = $('#editTestId').val();
    const data = {
        name: $('#editTestName').val().trim(),
        type: $('#editTestType').val(),
        priority: $('#editTestPriority').val(),
        status: $('#editTestStatus').val(),
        description: $('#editTestDescription').val().trim(),
        steps: $('#editTestSteps').val().trim(),
        expected_result: $('#editTestExpectedResult').val().trim()
    };

    if (!data.name) {
        showNotification('请输入测试用例名称', 'error');
        return;
    }

    showProgress('正在保存测试用例...');

    $.ajax({
        url: `/tests/api/${testId}/update`,
        method: 'PUT',
        contentType: 'application/json',
        data: JSON.stringify(data),
        success: function(response) {
            hideProgress();
            if (response.success) {
                showNotification(response.message, 'success');
                bootstrap.Modal.getInstance($('#editTestModal')[0]).hide();
                loadTests();
            } else {
                showNotification(response.message, 'error');
            }
        },
        error: function(xhr, status, error) {
            hideProgress();
            let errorMessage = '保存测试用例失败';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            }
            showNotification(errorMessage, 'error');
        }
    });
}

// 删除测试用例
function deleteTest(testId, testName) {
    if (confirm(`确定要删除测试用例 "${testName}" 吗？此操作不可恢复。`)) {
        showProgress('正在删除测试用例...');

        $.ajax({
            url: `/tests/api/${testId}/delete`,
            method: 'DELETE',
            success: function(response) {
                hideProgress();
                if (response.success) {
                    showNotification(response.message, 'success');
                    loadTests();
                } else {
                    showNotification(response.message, 'error');
                }
            },
            error: function(xhr, status, error) {
                hideProgress();
                let errorMessage = '删除测试用例失败';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                showNotification(errorMessage, 'error');
            }
        });
    }
}

// 执行测试用例
function executeTest(testId) {
    showNotification('测试执行功能正在开发中...', 'info');
    // TODO: 实现测试执行功能
}

// 显示需求详情
function showRequirementDetails(requirementId) {
    if (!requirementId) {
        showNotification('需求ID无效', 'error');
        return;
    }

    $.get(`/requirements/api/${requirementId}`, function(response) {
        if (response.success) {
            const req = response.data;
            const content = `
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>编号:</strong> ${req.number || '-'}</p>
                        <p><strong>类型:</strong> ${req.type || '-'}</p>
                        <p><strong>优先级:</strong> ${req.priority || '-'}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>状态:</strong> ${req.status || '-'}</p>
                        <p><strong>章节:</strong> ${req.section || '-'}</p>
                    </div>
                </div>
                <div class="mt-3">
                    <p><strong>标题:</strong></p>
                    <div class="border p-3 bg-light">${req.title || '无标题'}</div>
                </div>
                <div class="mt-3">
                    <p><strong>描述:</strong></p>
                    <div class="border p-3 bg-light" style="max-height: 300px; overflow-y: auto;">${req.description || '无描述'}</div>
                </div>
            `;

            $('#requirementDetailContent').html(content);
            const modal = new bootstrap.Modal($('#requirementDetailModal')[0]);
            modal.show();
        } else {
            showNotification('获取需求详情失败: ' + response.message, 'error');
        }
    }).fail(function() {
        showNotification('获取需求详情失败', 'error');
    });
}
</script>
{% endblock %}