{% extends "base.html" %}

{% block title %}自动化测试用例 - AI智能测试平台{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="/">首页</a></li>
<li class="breadcrumb-item"><a href="/tests">测试管理</a></li>
<li class="breadcrumb-item active">自动化测试用例</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-robot me-2"></i>自动化测试用例
            </h1>
            <div class="btn-group">
                <button class="btn btn-primary" onclick="showGenerateModal()">
                    <i class="fas fa-magic me-1"></i>生成自动化测试
                </button>
                <button class="btn btn-success" onclick="showCreateModal()">
                    <i class="fas fa-plus me-1"></i>创建自动化测试
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 搜索和过滤器 -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="input-group">
            <span class="input-group-text"><i class="fas fa-search"></i></span>
            <input type="text" class="form-control" id="searchInput" placeholder="搜索自动化测试用例...">
        </div>
    </div>
    <div class="col-md-2">
        <select class="form-select" id="statusFilter">
            <option value="">全部状态</option>
            <option value="draft">草稿</option>
            <option value="ready">就绪</option>
            <option value="running">运行中</option>
            <option value="passed">通过</option>
            <option value="failed">失败</option>
        </select>
    </div>
    <div class="col-md-2">
        <select class="form-select" id="typeFilter">
            <option value="">全部类型</option>
            <option value="unit">单元测试</option>
            <option value="integration">集成测试</option>
            <option value="api">API测试</option>
            <option value="ui">UI测试</option>
        </select>
    </div>
    <div class="col-md-2">
        <select class="form-select" id="pageSizeSelect" onchange="changePageSize()">
            <option value="10" selected>10条</option>
            <option value="30">30条</option>
            <option value="50">50条</option>
        </select>
    </div>
    <div class="col-md-2 text-end">
        <button class="btn btn-outline-secondary" onclick="refreshTests()">
            <i class="fas fa-sync me-1"></i>刷新
        </button>
    </div>
</div>

<!-- 自动化测试用例列表 -->
<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th width="40">
                            <input type="checkbox" id="headerCheckbox" onchange="toggleSelectAll()">
                        </th>
                        <th>测试用例名称</th>
                        <th>类型</th>
                        <th>状态</th>
                        <th>关联需求</th>
                        <th>最后执行</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="automationTestsTableBody">
                    <!-- 自动化测试用例列表将通过JavaScript动态加载 -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 分页 -->
<div class="d-flex justify-content-between align-items-center mt-4">
    <div id="batchActions" style="display: none;">
        <button class="btn btn-danger" onclick="batchDelete()">
            <i class="fas fa-trash me-1"></i>批量删除
        </button>
        <button class="btn btn-success" onclick="batchExecute()">
            <i class="fas fa-play me-1"></i>批量执行
        </button>
    </div>
    <nav>
        <ul class="pagination" id="pagination">
            <!-- 分页将通过JavaScript动态生成 -->
        </ul>
    </nav>
</div>

<!-- 生成自动化测试模态框 -->
<div class="modal fade" id="generateModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">生成自动化测试用例</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label for="requirementSelect" class="form-label">选择需求功能点</label>
                        <select class="form-select" id="requirementSelect" multiple size="8">
                            <!-- 需求选项将通过JavaScript动态加载 -->
                        </select>
                        <div class="form-text">按住Ctrl键可多选</div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="automationTestType" class="form-label">自动化测试类型</label>
                                <select class="form-select" id="automationTestType">
                                    <option value="unit">单元测试</option>
                                    <option value="integration">集成测试</option>
                                    <option value="api">API测试</option>
                                    <option value="ui">UI测试</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="generateThreads" class="form-label">并发线程数</label>
                                <input type="number" class="form-control" id="generateThreads" min="1" max="10" value="4">
                                <div class="form-text">用于并发生成测试用例</div>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="includeSetup" checked>
                            <label class="form-check-label" for="includeSetup">
                                生成测试环境设置代码
                            </label>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="includeTeardown" checked>
                            <label class="form-check-label" for="includeTeardown">
                                生成测试清理代码
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="generateAutomationTests()">开始生成</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentPage = 1;
let currentSearch = '';
let currentStatusFilter = '';
let currentTypeFilter = '';
let selectedTests = new Set();

$(document).ready(function() {
    loadAutomationTests();
    loadRequirements();

    // 搜索输入框事件
    $('#searchInput').on('input', debounce(function() {
        currentSearch = $(this).val();
        currentPage = 1;
        loadAutomationTests();
    }, 500));

    // 过滤器事件
    $('#statusFilter, #typeFilter').change(function() {
        currentPage = 1;
        loadAutomationTests();
    });
});

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 加载自动化测试用例列表
function loadAutomationTests() {
    const pageSize = parseInt($('#pageSizeSelect').val()) || 10;
    const params = {
        page: currentPage,
        page_size: pageSize,
        automation_only: true  // 只获取自动化测试用例
    };

    if (currentSearch) params.search = currentSearch;
    if ($('#statusFilter').val()) params.status = $('#statusFilter').val();
    if ($('#typeFilter').val()) params.type = $('#typeFilter').val();

    $.get('/tests/api/list', params, function(response) {
        if (response.success) {
            renderAutomationTestsTable(response.data.records);
            initPagination('pagination', response.data.total_pages, currentPage, function(page) {
                currentPage = page;
                loadAutomationTests();
            });
        } else {
            showNotification('加载自动化测试用例失败: ' + response.message, 'error');
        }
    }).fail(function() {
        showNotification('加载自动化测试用例失败', 'error');
    });
}

// 渲染自动化测试用例表格
function renderAutomationTestsTable(tests) {
    const tbody = $('#automationTestsTableBody');
    tbody.empty();

    if (tests.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="7" class="text-center text-muted py-4">
                    <i class="fas fa-robot fa-2x mb-2"></i><br>
                    暂无自动化测试用例
                </td>
            </tr>
        `);
        return;
    }

    tests.forEach(function(test) {
        const statusBadge = getStatusBadge(test.status);
        const typeBadge = getTypeBadge(test.type);
        
        const row = $(`
            <tr>
                <td>
                    <input type="checkbox" class="test-checkbox" value="${test.id}" onchange="updateSelection()">
                </td>
                <td>
                    <strong>${test.name || '未命名测试'}</strong>
                    <br><small class="text-muted">${test.description || '暂无描述'}</small>
                </td>
                <td>${typeBadge}</td>
                <td>${statusBadge}</td>
                <td>
                    ${test.requirement_ids && test.requirement_ids.length > 0 ? 
                        `<a href="javascript:void(0)" onclick="showRequirementDetails('${test.requirement_ids[0]}')" class="text-decoration-none">
                            <small>${test.requirement_name || '关联需求'}</small>
                        </a>` : 
                        '<small class="text-muted">无关联需求</small>'
                    }
                </td>
                <td>
                    <small class="text-muted">${test.last_executed ? formatDate(test.last_executed) : '未执行'}</small>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="viewTest('${test.id}')" title="查看详情">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-success" onclick="executeTest('${test.id}')" title="执行测试">
                            <i class="fas fa-play"></i>
                        </button>
                        <button class="btn btn-outline-warning" onclick="editTest('${test.id}')" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteTest('${test.id}')" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `);
        tbody.append(row);
    });
}

// 其他函数（状态徽章、类型徽章、日期格式化等）
function getStatusBadge(status) {
    const badges = {
        'draft': '<span class="badge bg-secondary">草稿</span>',
        'ready': '<span class="badge bg-primary">就绪</span>',
        'running': '<span class="badge bg-warning">运行中</span>',
        'passed': '<span class="badge bg-success">通过</span>',
        'failed': '<span class="badge bg-danger">失败</span>'
    };
    return badges[status] || '<span class="badge bg-light text-dark">未知</span>';
}

function getTypeBadge(type) {
    const badges = {
        'unit': '<span class="badge bg-info">单元测试</span>',
        'integration': '<span class="badge bg-warning">集成测试</span>',
        'api': '<span class="badge bg-success">API测试</span>',
        'ui': '<span class="badge bg-primary">UI测试</span>'
    };
    return badges[type] || '<span class="badge bg-light text-dark">其他</span>';
}

function formatDate(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

// 刷新列表
function refreshTests() {
    loadAutomationTests();
}

// 改变分页大小
function changePageSize() {
    currentPage = 1;
    loadAutomationTests();
}

// 其他功能函数（待实现）
function showGenerateModal() {
    showNotification('自动化测试生成功能正在开发中...', 'info');
}

function showCreateModal() {
    showNotification('创建自动化测试功能正在开发中...', 'info');
}

function generateAutomationTests() {
    showNotification('自动化测试生成功能正在开发中...', 'info');
}

function toggleSelectAll() {
    showNotification('批量操作功能正在开发中...', 'info');
}

function updateSelection() {
    // 更新选择状态
}

function batchDelete() {
    showNotification('批量删除功能正在开发中...', 'info');
}

function batchExecute() {
    showNotification('批量执行功能正在开发中...', 'info');
}

function viewTest(testId) {
    showNotification('查看测试详情功能正在开发中...', 'info');
}

function editTest(testId) {
    showNotification('编辑测试功能正在开发中...', 'info');
}

function deleteTest(testId) {
    showNotification('删除测试功能正在开发中...', 'info');
}

function executeTest(testId) {
    showNotification('执行测试功能正在开发中...', 'info');
}

function loadRequirements() {
    // 加载需求列表（复用测试管理页面的逻辑）
}

function showRequirementDetails(requirementId) {
    // 显示需求详情（复用测试管理页面的逻辑）
}
</script>
{% endblock %}
